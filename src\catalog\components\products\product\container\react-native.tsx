import React, { memo, useState } from 'react';
import {
  View,
  TouchableOpacity,
  ImageBackground,
  Text,
  StyleSheet,
} from 'react-native';
import { ProductType as CatalogProductType } from '../../../../types/products';
import { adaptCatalogProductToApp } from '../../../../utils/type-adapters';
import { theme } from '../../../../../constants';
import { text } from '../../../../../text';
import { useAppNavigation } from '../../../../../hooks';
import { responsiveWidth } from 'react-native-responsive-dimensions';

type Props = {
  product: CatalogProductType;
  onAddToCart?: (product: CatalogProductType) => void;
  onToggleWishlist?: (product: CatalogProductType) => void;
};

const CatalogProductContainer: React.FC<Props> = ({
  product,
  onAddToCart,
  onToggleWishlist,
}): JSX.Element => {
  const navigation = useAppNavigation();
  const [imageError, setImageError] = useState(false);
  
  // Convert catalog product to app product for navigation
  const appProduct = adaptCatalogProductToApp(product);
  const blockWidth = responsiveWidth(50) - 20 - 7.5;
  
  // Get product details
  const firstItem = product.items[0];
  const firstPrice = firstItem?.prices[0];
  const hasPromotion = firstPrice && firstPrice.realPrice > firstPrice.promotionalPrice;
  const promotionPercentage = hasPromotion 
    ? Math.round(((firstPrice.realPrice - firstPrice.promotionalPrice) / firstPrice.realPrice) * 100)
    : 0;

  const handlePress = () => {
    navigation.navigate('Product', { item: appProduct });
  };

  const handleAddToCart = () => {
    onAddToCart?.(product);
  };

  const handleToggleWishlist = () => {
    onToggleWishlist?.(product);
  };

  const productImage = !imageError && firstItem?.image 
    ? { uri: firstItem.image }
    : require('../../../../../assets/icon.png'); // Fallback image

  return (
    <TouchableOpacity
      style={[styles.container, { width: blockWidth }]}
      onPress={handlePress}
      activeOpacity={0.8}
    >
      {/* Product Image */}
      <ImageBackground
        source={productImage}
        style={styles.imageContainer}
        imageStyle={styles.image}
        resizeMode="cover"
        onError={() => setImageError(true)}
      >
        {/* Promotion Badge */}
        {hasPromotion && (
          <View style={styles.promotionBadge}>
            <Text style={styles.promotionText}>-{promotionPercentage}%</Text>
          </View>
        )}
        
        {/* Wishlist Button */}
        <TouchableOpacity
          style={styles.wishlistButton}
          onPress={handleToggleWishlist}
          hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
        >
          <Text style={styles.wishlistIcon}>♡</Text>
        </TouchableOpacity>
        
        {/* Out of Stock Overlay */}
        {!firstItem?.inStock && (
          <View style={styles.outOfStockOverlay}>
            <Text style={styles.outOfStockText}>Out of Stock</Text>
          </View>
        )}
      </ImageBackground>

      {/* Product Info */}
      <View style={styles.productInfo}>
        {/* Brand Name */}
        {product.brand && (
          <text.T14 style={styles.brandText} numberOfLines={1}>
            {product.brand.name}
          </text.T14>
        )}
        
        {/* Product Name */}
        <text.T16 style={styles.productName} numberOfLines={2}>
          {product.name}
        </text.T16>
        
        {/* Price */}
        <View style={styles.priceContainer}>
          <text.T16 style={styles.currentPrice}>
            ${firstPrice?.promotionalPrice?.toFixed(2) || '0.00'}
          </text.T16>
          {hasPromotion && (
            <text.T14 style={styles.oldPrice}>
              ${firstPrice?.realPrice?.toFixed(2)}
            </text.T14>
          )}
        </View>
        
        {/* Add to Cart Button */}
        {firstItem?.inStock && (
          <TouchableOpacity
            style={styles.addToCartButton}
            onPress={handleAddToCart}
            activeOpacity={0.8}
          >
            <Text style={styles.addToCartText}>Add to Cart</Text>
          </TouchableOpacity>
        )}
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: theme.colors.white,
    borderRadius: 5,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  imageContainer: {
    width: '100%',
    height: 200,
    padding: 10,
    marginBottom: 14,
    alignItems: 'flex-end',
  },
  image: {
    borderRadius: 5,
    backgroundColor: theme.colors.imageBackground,
  },
  promotionBadge: {
    position: 'absolute',
    top: 10,
    left: 10,
    backgroundColor: theme.colors.red,
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 4,
  },
  promotionText: {
    color: theme.colors.white,
    fontSize: 12,
    fontWeight: 'bold',
  },
  wishlistButton: {
    position: 'absolute',
    top: 10,
    right: 10,
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  wishlistIcon: {
    fontSize: 18,
    color: theme.colors.mainColor,
  },
  outOfStockOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 5,
  },
  outOfStockText: {
    color: theme.colors.white,
    fontSize: 16,
    fontWeight: 'bold',
  },
  productInfo: {
    paddingHorizontal: 12,
    paddingBottom: 12,
  },
  brandText: {
    color: theme.colors.textColor,
    opacity: 0.7,
    marginBottom: 4,
  },
  productName: {
    color: theme.colors.mainColor,
    fontWeight: '600',
    marginBottom: 8,
    lineHeight: 20,
  },
  priceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  currentPrice: {
    color: theme.colors.mainColor,
    fontWeight: 'bold',
    marginRight: 8,
  },
  oldPrice: {
    color: theme.colors.textColor,
    textDecorationLine: 'line-through',
    opacity: 0.6,
  },
  addToCartButton: {
    backgroundColor: theme.colors.mainColor,
    borderRadius: 5,
    paddingVertical: 8,
    alignItems: 'center',
  },
  addToCartText: {
    color: theme.colors.white,
    fontSize: 14,
    fontWeight: '600',
  },
});

export default memo(CatalogProductContainer);
