// React Native compatible HTTP methods for catalog module
interface RequestConfig {
  headers?: Record<string, string>;
  timeout?: number;
}

interface ApiResponse<T = any> {
  data: T;
  status: number;
  statusText: string;
}

class HttpError extends Error {
  status: number;
  statusText: string;

  constructor(status: number, statusText: string, message?: string) {
    super(message || `HTTP Error ${status}: ${statusText}`);
    this.status = status;
    this.statusText = statusText;
    this.name = 'HttpError';
  }
}

// Base URL configuration - you can update this to match your API
const BASE_URL = 'https://your-api-base-url.com/api'; // Update this with your actual API base URL

const defaultHeaders = {
  'Content-Type': 'application/json',
  Accept: 'application/json',
};

async function makeRequest<T>(
  url: string,
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH',
  config: RequestConfig = {},
  body?: any,
): Promise<ApiResponse<T>> {
  const fullUrl = url.startsWith('http') ? url : `${BASE_URL}${url}`;

  const requestConfig: RequestInit = {
    method,
    headers: {
      ...defaultHeaders,
      ...config.headers,
    },
  };

  if (body && method !== 'GET') {
    requestConfig.body = JSON.stringify(body);
  }

  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(
      () => controller.abort(),
      config.timeout || 10000,
    );

    const response = await fetch(fullUrl, {
      ...requestConfig,
      signal: controller.signal,
    });

    clearTimeout(timeoutId);

    if (!response.ok) {
      throw new HttpError(response.status, response.statusText);
    }

    const data = await response.json();

    return {
      data,
      status: response.status,
      statusText: response.statusText,
    };
  } catch (error) {
    if (error instanceof HttpError) {
      throw error;
    }

    if (error instanceof Error) {
      if (error.name === 'AbortError') {
        throw new HttpError(408, 'Request Timeout', 'Request timed out');
      }
      throw new HttpError(0, 'Network Error', error.message);
    }

    throw new HttpError(0, 'Unknown Error', 'An unknown error occurred');
  }
}

export async function GET<T = any>(
  url: string,
  config: RequestConfig = {},
): Promise<ApiResponse<T>> {
  return makeRequest<T>(url, 'GET', config);
}

export async function POST<T = any>(
  url: string,
  body?: any,
  config: RequestConfig = {},
): Promise<ApiResponse<T>> {
  return makeRequest<T>(url, 'POST', config, body);
}

export async function PUT<T = any>(
  url: string,
  body?: any,
  config: RequestConfig = {},
): Promise<ApiResponse<T>> {
  return makeRequest<T>(url, 'PUT', config, body);
}

export async function DELETE<T = any>(
  url: string,
  config: RequestConfig = {},
): Promise<ApiResponse<T>> {
  return makeRequest<T>(url, 'DELETE', config);
}

export async function PATCH<T = any>(
  url: string,
  body?: any,
  config: RequestConfig = {},
): Promise<ApiResponse<T>> {
  return makeRequest<T>(url, 'PATCH', config, body);
}

// Export types for use in other files
export type {RequestConfig, ApiResponse};
export {HttpError};
