import React, {useState} from 'react';
import Modal from 'react-native-modal';
import {View, FlatList, TouchableOpacity} from 'react-native';

import {text} from '../text';
import {svg} from '../assets/svg';
import {components} from '../components';
import {theme, sortingBy} from '../constants';
import type {RootStackParamList} from '../types';
import {useGetProductsQuery} from '../store/slices/apiSlice';
import type {NativeStackScreenProps} from '@react-navigation/native-stack';

type Props = NativeStackScreenProps<RootStackParamList, 'Shop'>;

const Shop: React.FC<Props> = ({route, navigation}): JSX.Element => {
  const {data, error, isLoading} = useGetProductsQuery();
  const {title, products} = route.params;

  const [showModal, setShowModal] = useState(false);

  if (isLoading) {
    return <components.Loader />;
  }

  const renderStatusBar = (): JSX.Element => {
    return (
      <components.StatusBar
        backgroundColor={theme.colors.transparent}
        barStyle='dark-content'
      />
    );
  };

  const renderHeader = (): JSX.Element => {
    return (
      <components.Header
        burgerIcon={true}
        basket={true}
        bottomLine={true}
        title={title}
      />
    );
  };

  const renderCatalogButton = (): JSX.Element => {
    return (
      <TouchableOpacity
        style={{
          backgroundColor: theme.colors.mainColor,
          paddingHorizontal: 20,
          paddingVertical: 12,
          borderRadius: 8,
          margin: 15,
          alignItems: 'center',
        }}
        onPress={() => navigation.navigate('CatalogProducts')}
      >
        <text.T16 style={{color: theme.colors.white, fontWeight: '600'}}>
          Browse Catalog Products
        </text.T16>
      </TouchableOpacity>
    );
  };

  const renderOptions = () => {
    return (
      <View
        style={{
          flexDirection: 'row',
          justifyContent: 'space-between',
          alignItems: 'center',
        }}
      >
        <TouchableOpacity
          style={{
            paddingTop: 22,
            paddingRight: 20,
            paddingBottom: 15,
            paddingLeft: 20,
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'flex-start',
          }}
          onPress={() => navigation.navigate('Filter')}
        >
          <svg.SettingsSvg />
          <text.T14 style={{marginLeft: 8}}>Filters</text.T14>
        </TouchableOpacity>
        <TouchableOpacity
          style={{
            paddingTop: 22,
            paddingRight: 20,
            paddingBottom: 15,
            paddingLeft: 20,
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'flex-start',
          }}
          onPress={() => setShowModal(true)}
        >
          <text.T14 style={{marginRight: 7}}>Sorting by</text.T14>
          <svg.ArrowBottomSvg />
        </TouchableOpacity>
      </View>
    );
  };

  const renderContent = () => {
    return (
      <FlatList
        data={products}
        renderItem={({item}) => <components.ShopItem item={item} />}
        columnWrapperStyle={{justifyContent: 'space-between'}}
        numColumns={2}
        horizontal={false}
        contentContainerStyle={{
          paddingHorizontal: 20,
          flexGrow: 1,
        }}
      />
    );
  };

  const renderPopup = () => {
    return (
      <Modal
        isVisible={showModal}
        onBackdropPress={() => setShowModal(false)}
        hideModalContentWhileAnimating={true}
        backdropTransitionOutTiming={0}
        style={{margin: 0}}
        animationIn='zoomIn'
        animationOut='zoomOut'
      >
        <View
          style={{
            backgroundColor: theme.colors.white,
            marginHorizontal: 40,
            borderRadius: 5,
            paddingLeft: 20,
            paddingVertical: 6,
          }}
        >
          {sortingBy.map((item, index, array) => {
            return (
              <TouchableOpacity
                key={index}
                style={{
                  height: 49,
                  borderBottomWidth: array.length - 1 === index ? 0 : 1,
                  marginBottom: 4,
                  borderBottomColor: theme.colors.lightBlue,
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  paddingRight: 20,
                }}
                onPress={() => {
                  // setSort(item.title);
                  setShowModal(false);
                }}
              >
                <text.T14
                  style={{
                    color: theme.colors.mainColor,
                  }}
                >
                  {item.title}
                </text.T14>
                <View
                  style={{
                    width: 16,
                    height: 16,
                    borderWidth: 1,
                    borderRadius: 8,
                    justifyContent: 'center',
                    alignItems: 'center',
                    borderColor: theme.colors.textColor,
                  }}
                >
                  <View
                    style={{
                      // backgroundColor:
                      //   sort === item.title
                      //     ? theme.colors.mainColor
                      //     : theme.colors.white,
                      width: 10,
                      height: 10,
                      borderRadius: 5,
                    }}
                  />
                </View>
              </TouchableOpacity>
            );
          })}
        </View>
      </Modal>
    );
  };

  const renderHomeIndicator = (): JSX.Element => {
    return <components.HomeIndicator />;
  };

  return (
    <components.SmartView>
      {renderStatusBar()}
      {renderHeader()}
      {renderCatalogButton()}
      {renderOptions()}
      {renderContent()}
      {renderHomeIndicator()}
      {renderPopup()}
    </components.SmartView>
  );
};

export default Shop;
