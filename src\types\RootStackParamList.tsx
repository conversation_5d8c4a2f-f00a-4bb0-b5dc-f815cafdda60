import {ProductType} from './ProductType';

export type RootStackParamList = {
  FAQ: undefined;
  Filter: undefined;
  SignIn: undefined;
  SignUp: undefined;
  Search: undefined;
  Reviews: undefined;
  Checkout: undefined;
  MyAddress: undefined;
  Onboarding: undefined;
  EditProfile: undefined;
  NewPassword: undefined;
  OrderFailed: undefined;
  AddANewCard: undefined;
  OrderHistory: undefined;
  LeaveAReview: undefined;
  MyPromocodes: undefined;
  TabNavigator: undefined;
  Notifications: undefined;
  LeaveAReviews: undefined;
  PaymentMethod: undefined;
  ForgotPassword: undefined;
  TrackYourOrder: undefined;
  AddANewAddress: undefined;
  OrderSuccessful: undefined;
  ConfirmationCode: undefined;
  Product: {item: ProductType};
  CatalogProducts: undefined;
  MyPromocodesEmpty: undefined;
  SignUpAccountCreated: undefined;
  VerifyYourPhoneNumber: undefined;
  Description: {description: string};
  ForgotPasswordSentEmail: undefined;
  Shop: {title: string; products: ProductType[]};
};
